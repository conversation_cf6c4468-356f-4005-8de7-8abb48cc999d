// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:chatty/main.dart';

void main() {
  testWidgets('<PERSON><PERSON><PERSON><PERSON> renders correctly', (WidgetTester tester) async {
    await tester.pumpWidget(const ChattyApp());

    expect(find.text('Chatty Music'), findsOneWidget);
    expect(find.text('Now Playing'), findsOneWidget);
    expect(find.text('Shuffle'), findsOneWidget);
    expect(find.text('Favorites'), findsOneWidget);
  });

  testWidgets('LiquidGlass components render', (WidgetTester tester) async {
    await tester.pumpWidget(const ChattyApp());

    expect(find.byType(LiquidGlass), findsAtLeast(5));
    expect(find.byIcon(Icons.search_rounded), findsOneWidget);
    expect(find.byIcon(Icons.music_note_rounded), findsOneWidget);
  });
}
