import 'package:flutter/material.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart' as lgr;
import 'dart:math';

void main() => runApp(const ChattyApp());

class ChattyApp extends StatelessWidget {
  const ChattyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        brightness: Brightness.dark,
        scaffoldBackgroundColor: const Color(0xFF0B0C10),
        useMaterial3: true,
        textTheme: Theme.of(
          context,
        ).textTheme.apply(bodyColor: Colors.white, displayColor: Colors.white),
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _glassOn = true;
  bool _vivid = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          const _Background(),
          _Foreground(
            glassOn: _glassOn,
            vivid: _vivid,
            onToggleGlass: () => setState(() => _glassOn = !_glassOn),
            onToggleVivid: () => setState(() => _vivid = !_vivid),
          ),
        ],
      ),
    );
  }
}

class _Background extends StatelessWidget {
  const _Background();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF0F1624), Color(0xFF1B2735)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        children: [
          // 柔光彩斑
          Positioned(
            left: -60,
            top: -40,
            child: _GlowCircle(
              color: const Color(0xFF5B9DF9).withValues(alpha: 0.35),
              size: 220,
            ),
          ),
          Positioned(
            right: -50,
            bottom: -20,
            child: _GlowCircle(
              color: const Color(0xFF9B5BF9).withValues(alpha: 0.28),
              size: 260,
            ),
          ),
          Positioned(
            left: -40,
            bottom: 120,
            child: _GlowCircle(
              color: const Color(0xFF4CE5B1).withValues(alpha: 0.22),
              size: 180,
            ),
          ),
        ],
      ),
    );
  }
}

class _GlowCircle extends StatelessWidget {
  final Color color;
  final double size;
  const _GlowCircle({required this.color, required this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [color, Colors.transparent],
          stops: const [0.0, 1.0],
        ),
      ),
    );
  }
}

class _Foreground extends StatelessWidget {
  final bool glassOn;
  final bool vivid;
  final VoidCallback onToggleGlass;
  final VoidCallback onToggleVivid;
  const _Foreground({
    required this.glassOn,
    required this.vivid,
    required this.onToggleGlass,
    required this.onToggleVivid,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 顶部标题玻璃条
            LiquidGlass(
              enabled: glassOn,
              vivid: vivid,
              height: 56,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const Icon(Icons.graphic_eq_rounded),
                    const SizedBox(width: 12),
                    const Text(
                      'Chatty Music',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const Spacer(),
                    // 开关按钮
                    IconButton(
                      tooltip: glassOn ? '关闭玻璃效果' : '开启玻璃效果',
                      icon: Icon(
                        glassOn
                            ? Icons.blur_on_rounded
                            : Icons.blur_off_rounded,
                      ),
                      onPressed: onToggleGlass,
                    ),
                    const SizedBox(width: 4),
                    // 增强模式按钮
                    IconButton(
                      tooltip: vivid ? '关闭增强模式' : '开启增强模式',
                      icon: Icon(
                        vivid
                            ? Icons.auto_awesome_rounded
                            : Icons.auto_awesome_outlined,
                      ),
                      onPressed: onToggleVivid,
                    ),
                    const Icon(Icons.search_rounded),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Now Playing 玻璃卡片
            LiquidGlass(
              enabled: glassOn,
              vivid: vivid,
              height: 170,
              child: const _NowPlayingCard(),
            ),

            const SizedBox(height: 16),

            // 快捷操作
            Row(
              children: [
                Expanded(
                  child: LiquidGlass(
                    enabled: glassOn,
                    height: 64,
                    child: const _QuickAction(
                      icon: Icons.shuffle_rounded,
                      label: 'Shuffle',
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: LiquidGlass(
                    enabled: glassOn,
                    height: 64,
                    child: const _QuickAction(
                      icon: Icons.favorite_rounded,
                      label: 'Favorites',
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 列表玻璃容器
            Expanded(
              child: LiquidGlass(
                enabled: glassOn,
                vivid: vivid,
                child: const _MockSongList(),
              ),
            ),
            const SizedBox(height: 12),

            // 底部迷你播放器玻璃条
            LiquidGlass(
              enabled: glassOn,
              vivid: vivid,
              height: 64,
              child: const _MiniPlayerBar(),
            ),
          ],
        ),
      ),
    );
  }
}

class LiquidGlass extends StatelessWidget {
  final Widget child;
  final double? height;
  final double thickness;
  final double opacity;
  final BorderRadius borderRadius;
  final bool enabled;
  final bool vivid;

  const LiquidGlass({
    super.key,
    required this.child,
    this.height,
    this.thickness = 20,
    this.opacity = 0.12,
    this.borderRadius = const BorderRadius.all(Radius.circular(20)),
    this.enabled = true,
    this.vivid = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget content;
    if (enabled) {
      final settings = vivid
          ? lgr.LiquidGlassSettings(
              thickness: max(20, thickness * 1.6),
              glassColor: Colors.white.withValues(
                alpha: max(0.20, opacity * 1.5),
              ),
              lightAngle: 0.52 * pi,
              lightIntensity: 0.7,
              ambientStrength: 0.22,
              blur: 10,
            )
          : lgr.LiquidGlassSettings(
              thickness: thickness,
              glassColor: Colors.white.withValues(alpha: opacity),
              lightAngle: 0.6 * pi,
              lightIntensity: 0.15,
              ambientStrength: 0.02,
              blur: 5,
            );

      content = lgr.LiquidGlass(
        shape: lgr.LiquidRoundedSuperellipse(
          borderRadius: borderRadius.topLeft,
        ),
        glassContainsChild: false,
        settings: settings,
        child: child,
      );
    } else {
      // 关闭效果时，用常规毛玻璃风格的占位：半透明+描边，保持布局
      content = Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(
            alpha: vivid ? max(0.18, opacity * 1.3) : opacity,
          ),
          borderRadius: borderRadius,
          border: Border.all(
            color: Colors.white.withValues(
              alpha: vivid ? max(0.32, opacity * 2.2) : opacity * 2,
            ),
            width: vivid ? 1.2 : 1,
          ),
        ),
        child: child,
      );
    }

    if (height != null) {
      return SizedBox(height: height, child: content);
    }
    return content;
  }
}

class _NowPlayingCard extends StatelessWidget {
  const _NowPlayingCard();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 模拟专辑封面
          Container(
            width: 110,
            height: 110,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF5B9DF9), Color(0xFF9B5BF9)],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Now Playing',
                  style: TextStyle(color: Colors.white.withValues(alpha: 0.9)),
                ),
                const SizedBox(height: 4),
                const Text(
                  'A Beautiful Placeholder',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(999),
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: FractionallySizedBox(
                      widthFactor: 0.35,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.55),
                          borderRadius: BorderRadius.circular(999),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _QuickAction extends StatelessWidget {
  final IconData icon;
  final String label;
  const _QuickAction({required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon),
          const SizedBox(width: 10),
          Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
}

class _MockSongList extends StatelessWidget {
  const _MockSongList();

  @override
  Widget build(BuildContext context) {
    final items = List.generate(12, (i) => 'Track ${i + 1} — Artist');
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 4),
      itemCount: items.length,
      separatorBuilder: (_, __) =>
          Divider(height: 1, color: Colors.white.withValues(alpha: 0.08)),
      itemBuilder: (context, i) {
        return ListTile(
          leading: Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: const LinearGradient(
                colors: [Color(0xFF5B9DF9), Color(0xFF4CE5B1)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          title: Text(items[i], maxLines: 1, overflow: TextOverflow.ellipsis),
          trailing: const Icon(Icons.play_arrow_rounded),
          onTap: () {},
        );
      },
    );
  }
}

class _MiniPlayerBar extends StatelessWidget {
  const _MiniPlayerBar();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          const Icon(Icons.music_note_rounded),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Beautiful Placeholder — Artist',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 12),
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.18),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.35),
                width: 1,
              ),
            ),
            child: const Icon(Icons.play_arrow_rounded, size: 22),
          ),
        ],
      ),
    );
  }
}
